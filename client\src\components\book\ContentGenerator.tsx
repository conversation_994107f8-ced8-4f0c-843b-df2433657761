import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { apiRequest } from "@/lib/queryClient";
import { GeneratedContent, ContentGenerationParams, WritingTone, WritingStyle, WritingLanguage, BookOutline, BookTitle, BookLanguage, TargetAudience } from "@shared/types";
import { CheckIcon, ChevronRightIcon, EditIcon, RefreshCcwIcon, ArrowRightIcon, Loader2Icon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ContentSkeleton, TitleListSkeleton } from "@/components/ui/skeleton";
import { GenerateContentButton, RegenerateButton, GenerateOutlineButton } from "@/components/ui/button-loading";
import BookCoverCard from "./BookCoverCard";

interface ContentGeneratorProps {
  selectedChapter: {
    mainChapterTitle: string;
    subChapterTitle: string;
    index: number;
    chapterIndex: number;
    subChapterIndex: number;
  } | null;
  bookTopic: string;
  generatedContent: GeneratedContent | null;
  onContentGenerated: (content: string) => void;
  contentTone: WritingTone;
  contentStyle: WritingStyle;
  contentLanguage: WritingLanguage;
  selectedLanguage: BookLanguage;
  targetAudience: TargetAudience;
  outline?: BookOutline | null;
  generatedChapters?: Record<string, boolean>;
  onChapterSelect?: (mainChapterTitle: string, subChapterTitle: string, index: number, chapterIndex: number, subChapterIndex: number) => void;
  nextChapterToGenerate?: { chapterIndex: number; subChapterIndex: number };
  // New props for title selection workflow
  workflowStep?: 'input' | 'title-selection' | 'outline-generated';
  generatedTitles?: BookTitle[];
  selectedTitle?: BookTitle | null;
  onTitleSelect?: (title: BookTitle) => void;
  onApplyParameters?: (title: BookTitle) => void;
  onGenerateOutline?: () => void;
  isGeneratingOutline?: boolean;
  onSaveIdea?: (title: BookTitle) => void;
  isSavingIdea?: boolean;
}

export default function ContentGenerator({
  selectedChapter,
  bookTopic,
  generatedContent,
  onContentGenerated,
  contentTone,
  contentStyle,
  contentLanguage,
  selectedLanguage,
  targetAudience,
  outline,
  generatedChapters,
  onChapterSelect,
  nextChapterToGenerate,
  workflowStep = 'input',
  generatedTitles = [],
  selectedTitle,
  onTitleSelect,
  onApplyParameters,
  onGenerateOutline,
  isGeneratingOutline = false,
  onSaveIdea,
  isSavingIdea = false
}: ContentGeneratorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (selectedChapter) {
      // Check if content already exists in localStorage
      const storageKey = `${selectedChapter.mainChapterTitle}-${selectedChapter.subChapterTitle}`;
      const existingContent = localStorage.getItem(`chapter-content-${storageKey}`);
      
      if (existingContent) {
        // If content exists in localStorage, use it instead of generating new content
        onContentGenerated(existingContent);
      } else if (!generatedContent) {
        // Only generate new content if it doesn't exist in localStorage
        generateContent();
      }
    }
  }, [selectedChapter]);

  const generateContent = async () => {
    if (!selectedChapter || !bookTopic) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const res = await apiRequest('POST', '/api/generate-chapter', {
        subChapterTitle: selectedChapter.subChapterTitle,
        mainChapterTitle: selectedChapter.mainChapterTitle,
        bookTopic,
        generationParams: {
          tone: contentTone,
          style: contentStyle,
          language: contentLanguage,
          targetLanguage: selectedLanguage,
          targetAudience: targetAudience
        }
      });
      
      const data = await res.json();
      onContentGenerated(data.subChapterContent);
      
      toast({
        title: "Content generated",
        description: "Your chapter content has been created successfully",
      });
    } catch (err) {
      console.error("Error generating content:", err);
      setError("Failed to generate content. Please try again.");
      
      toast({
        title: "Generation failed",
        description: "Could not generate chapter content. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const regenerateContent = async () => {
    if (!selectedChapter) return;
    setIsLoading(true);
    setError(null);

    try {
      // Clear any existing content from localStorage before regenerating
      if (selectedChapter) {
        const storageKey = `${selectedChapter.mainChapterTitle}-${selectedChapter.subChapterTitle}`;
        localStorage.removeItem(`chapter-content-${storageKey}`);
      }
      
      await generateContent();
    } catch (err) {
      console.error("Error regenerating content:", err);
      setError("Failed to regenerate content. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper to get user-friendly descriptions of the content parameters
  const getContentStyleDescription = () => {
    const toneLabels: Record<WritingTone, string> = {
      professional: "Professional",
      casual: "Casual",
      academic: "Academic",
      conversational: "Conversational",
      instructional: "Instructional"
    };
    
    const styleLabels: Record<WritingStyle, string> = {
      descriptive: "Descriptive",
      analytical: "Analytical",
      persuasive: "Persuasive",
      narrative: "Narrative",
      technical: "Technical"
    };
    
    const languageLabels: Record<WritingLanguage, string> = {
      simple: "Simple",
      intermediate: "Intermediate",
      advanced: "Advanced",
      technical: "Technical"
    };
    
    return {
      tone: toneLabels[contentTone],
      style: styleLabels[contentStyle],
      language: languageLabels[contentLanguage]
    };
  };

  // Rendering helper for markdown content
  const renderMarkdown = (content: string) => {
    if (!content) return "";

    // Step 1: Clean up unwanted artifacts
    let cleanContent = content
      // Remove markdown code block markers
      .replace(/```markdown\s*/gi, '')
      .replace(/```\s*/g, '')
      // Remove extra asterisks that aren't formatting
      .replace(/\*{3,}/g, '')
      // Remove extra hash symbols that aren't headers
      .replace(/#{4,}/g, '###')
      // Clean up double spaces
      .replace(/  +/g, ' ')
      .trim();

    // Step 2: Process headers first
    cleanContent = cleanContent
      .replace(/^### (.*$)/gim, '<h3 class="font-semibold text-xl mb-3 mt-6">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="font-semibold text-2xl mb-4 mt-8">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="font-bold text-3xl mb-5 mt-8">$1</h1>');

    // Step 3: Process blockquotes
    cleanContent = cleanContent
      .replace(/^\> (.*)$/gim, '<blockquote class="border-l-4 border-primary/30 pl-4 italic my-4">$1</blockquote>');

    // Step 4: Process bold and italic (before lists to avoid conflicts)
    cleanContent = cleanContent
      .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
      .replace(/\*([^*\n]+)\*/g, '<em>$1</em>');

    // Step 5: Process lists properly
    const lines = cleanContent.split('\n');
    const processedLines: string[] = [];
    let inList = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Check if this is a bullet point
      if (line.match(/^[\*\-] /)) {
        if (!inList) {
          processedLines.push('<ul class="list-disc ml-6 my-4 space-y-2">');
          inList = true;
        }
        const listContent = line.replace(/^[\*\-] /, '');
        processedLines.push(`<li class="leading-relaxed">${listContent}</li>`);
      }
      // Check if this is a numbered list
      else if (line.match(/^\d+\. /)) {
        if (!inList) {
          processedLines.push('<ol class="list-decimal ml-6 my-4 space-y-2">');
          inList = true;
        }
        const listContent = line.replace(/^\d+\. /, '');
        processedLines.push(`<li class="leading-relaxed">${listContent}</li>`);
      }
      // Regular content
      else {
        if (inList) {
          processedLines.push(inList ? '</ul>' : '</ol>');
          inList = false;
        }
        if (line.length > 0) {
          processedLines.push(line);
        } else {
          processedLines.push('');
        }
      }
    }

    // Close any open list
    if (inList) {
      processedLines.push('</ul>');
    }

    // Step 6: Process paragraphs
    const finalContent = processedLines.join('\n');
    const paragraphs = finalContent.split(/\n\s*\n/);

    const processedParagraphs = paragraphs.map(paragraph => {
      const trimmed = paragraph.trim();
      if (!trimmed) return '';

      // Don't wrap headers, lists, or blockquotes in paragraph tags
      if (trimmed.startsWith('<h') ||
          trimmed.startsWith('<ul') ||
          trimmed.startsWith('<ol') ||
          trimmed.startsWith('<blockquote') ||
          trimmed.includes('<li>')) {
        return trimmed;
      }

      return `<p class="mb-4 leading-relaxed">${trimmed}</p>`;
    });

    return processedParagraphs.filter(p => p.length > 0).join('\n\n');
  };

  const styleInfo = getContentStyleDescription();

  // Title Selection Panel Component
  const TitleSelectionPanel = () => {
    if (workflowStep !== 'title-selection') {
      return null;
    }

    // Show skeleton loading while titles are being generated
    if (generatedTitles.length === 0) {
      return (
        <div className="p-6 space-y-4 max-w-6xl mx-auto">
          <div className="text-center mb-6">
            <h2 className="text-xl font-semibold mb-2">Generating Your Book Titles</h2>
            <p className="text-muted-foreground">Creating 6 unique book concepts with comprehensive parameters...</p>
          </div>
          <TitleListSkeleton count={6} />
        </div>
      );
    }

    const handleApply = (title: BookTitle) => {
      // Call the parent's apply parameters function
      onApplyParameters?.(title);
    };

    return (
      <div className="p-6 space-y-6 max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-3">Select Your Book Concept</h2>
          <p className="text-muted-foreground text-lg">Choose the book approach that best fits your vision</p>
        </div>

        {/* Book Cover Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {generatedTitles.map((title) => (
            <BookCoverCard
              key={title.id}
              title={title}
              isSelected={selectedTitle?.id === title.id}
              onSelect={onTitleSelect || (() => {})}
              onApply={handleApply}
              isApplying={false}
              onSaveIdea={onSaveIdea}
              isSaving={isSavingIdea}
              originalTopic={bookTopic}
            />
          ))}
        </div>

        {/* Instructions */}
        {!selectedTitle && (
          <div className="text-center mt-8 p-4 bg-muted/30 rounded-lg">
            <p className="text-muted-foreground">
              Click on any book concept to see its complete parameters and generate the outline
            </p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="p-6 flex-1 flex flex-col">
      {/* Content Header */}
      {selectedChapter && (
        <div className="flex justify-between items-start mb-4">
          <div>
            <div className="text-sm text-muted-foreground font-medium mb-1">
              Chapter {selectedChapter.index}
            </div>
            <h2 className="text-xl md:text-2xl font-semibold">
              {selectedChapter.subChapterTitle}
            </h2>
            
            {/* Display content style when content is generated */}
            {generatedContent && !isLoading && (
              <div className="flex flex-wrap gap-2 mt-2">
                <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                  {styleInfo.tone}
                </div>
                <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                  {styleInfo.style}
                </div>
                <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                  {styleInfo.language}
                </div>
              </div>
            )}
          </div>
          
          {/* Generation Status */}
          {generatedContent && !isLoading && (
            <div className="bg-green-500/20 px-3 py-1 rounded-full text-green-500 text-xs flex items-center space-x-1">
              <CheckIcon className="h-4 w-4" />
              <span>Generated</span>
            </div>
          )}
        </div>
      )}
      
      {/* Loading State for Content */}
      {isLoading && (
        <div className="flex-1 flex justify-center items-center">
          <div className="text-center space-y-4">
            <LoadingSpinner 
              size="xl" 
              variant="gradient" 
              text="Writing chapter content" 
              showParticles={true}
            />
            <div className="max-w-md">
              <p className="text-muted-foreground mb-2">Creating engaging content...</p>
              <p className="text-xs text-muted-foreground">
                Style: {styleInfo.tone}, {styleInfo.style}, {styleInfo.language}
              </p>
            </div>
          </div>
        </div>
      )}
      
      {/* Error State for Content */}
      {error && !isLoading && (
        <div className="flex-1 flex justify-center items-center">
          <div className="text-center bg-destructive/10 p-6 rounded-lg max-w-md">
            <svg className="h-12 w-12 text-destructive mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-destructive">Generation Failed</h3>
            <p className="mt-1 text-muted-foreground">
              We couldn't generate content for this chapter. Please try again or select a different chapter.
            </p>
            <RegenerateButton 
              variant="outline" 
              className="mt-4" 
              isRegenerating={false}
              onClick={regenerateContent}
            >
              Try Again
            </RegenerateButton>
          </div>
        </div>
      )}
      
      {/* Empty State for Content */}
      {!selectedChapter && !isLoading && !error && (
        <div className="flex-1 flex justify-center items-center">
          {workflowStep === 'title-selection' ? (
            <TitleSelectionPanel />
          ) : (
            <div className="text-center max-w-md">
              <svg className="h-16 w-16 text-muted-foreground mx-auto opacity-40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-2 text-lg font-medium">No Content Selected</h3>
              <p className="mt-1 text-muted-foreground">
                {workflowStep === 'input' 
                  ? "Enter your book topic and generate titles to get started" 
                  : "Generate an outline and select a chapter to view content"
                }
              </p>
            </div>
          )}
        </div>
      )}
      
      {/* Generated Content */}
      {generatedContent && !isLoading && !error && (
        <div className="flex-1 bg-card p-6 rounded-lg border border-muted overflow-y-auto">
          <article 
            className="prose prose-invert max-w-none" 
            dangerouslySetInnerHTML={{ 
              __html: renderMarkdown(generatedContent.content || "")
            }}
          />
        </div>
      )}
      
      {/* Content Actions */}
      {generatedContent && !isLoading && !error && (
        <div className="mt-4 flex justify-between">
          <div className="flex space-x-2">
            <Button variant="outline" className="flex items-center gap-1">
              <EditIcon className="h-4 w-4" />
              <span>Edit</span>
            </Button>
            
            <RegenerateButton 
              variant="outline" 
              className="flex items-center gap-1"
              isRegenerating={false}
              onClick={regenerateContent}
            >
              <RefreshCcwIcon className="h-4 w-4" />
              <span>Regenerate</span>
            </RegenerateButton>
          </div>
          
          <div className="flex space-x-2">
            <Button variant="outline" className="flex items-center gap-1">
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
              </svg>
              <span>Export</span>
            </Button>
            
            <Button 
              className="flex items-center gap-1"
              onClick={() => {
                if (!selectedChapter || !outline) return;
                
                // Find the next chapter in sequence
                let nextChapterIndex = selectedChapter.chapterIndex;
                let nextSubChapterIndex = selectedChapter.subChapterIndex + 1;
                
                // If we've reached the end of subchapters in this chapter, move to the next chapter
                if (nextSubChapterIndex >= outline.chapters[nextChapterIndex].subchapters.length) {
                  nextChapterIndex++;
                  nextSubChapterIndex = 0;
                }
                
                // Only proceed if we haven't reached the end of all chapters
                if (nextChapterIndex < outline.chapters.length) {
                  const nextChapter = outline.chapters[nextChapterIndex];
                  const nextSubChapter = nextChapter.subchapters[nextSubChapterIndex];
                  
                  // Check if this next chapter is already generated
                  const isGenerated = generatedChapters ? generatedChapters[`${nextChapter.title}-${nextSubChapter}`] : false;
                  
                  if (isGenerated || (nextChapterIndex === nextChapterToGenerate?.chapterIndex && 
                      nextSubChapterIndex === nextChapterToGenerate?.subChapterIndex)) {
                    onChapterSelect && onChapterSelect(
                      nextChapter.title,
                      nextSubChapter,
                      nextChapterIndex + 1,
                      nextChapterIndex,
                      nextSubChapterIndex
                    );
                  } else {
                    toast({
                      title: "Sequential generation required",
                      description: "Please generate content for chapters in sequential order.",
                      variant: "destructive"
                    });
                  }
                } else {
                  toast({
                    title: "End of book reached",
                    description: "You've reached the end of your book outline.",
                  });
                }
              }}
            >
              <ChevronRightIcon className="h-4 w-4" />
              <span>Next Chapter</span>
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
