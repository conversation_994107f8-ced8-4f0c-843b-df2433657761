import { 
  collection, 
  doc, 
  addDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy, 
  Timestamp,
  DocumentData 
} from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import { SavedIdea, CreateSavedIdeaData, BookTitle } from '@shared/types';
import { onAuthStateChanged, User } from 'firebase/auth';

const SAVED_IDEAS_COLLECTION = 'savedIdeas';

/**
 * Get the current authenticated user
 */
function getCurrentUser(): Promise<User | null> {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      unsubscribe();
      resolve(user);
    });
  });
}

/**
 * Ensure user is authenticated before performing operations
 */
async function ensureAuthenticated(): Promise<User> {
  const user = auth.currentUser || await getCurrentUser();
  if (!user) {
    throw new Error('User must be authenticated to perform this operation');
  }
  return user;
}

export class SavedIdeasService {
  /**
   * Save a new book idea to Firestore
   */
  static async saveIdea(userId: string, ideaData: CreateSavedIdeaData): Promise<string> {
    try {
      // Ensure user is authenticated
      const user = await ensureAuthenticated();
      
      // Verify the userId matches the authenticated user
      if (user.uid !== userId) {
        throw new Error('User ID mismatch. Cannot save idea for different user.');
      }

      const ideaToSave = {
        ...ideaData,
        userId: user.uid, // Use authenticated user's ID
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      };

      console.log('Saving idea for user:', user.uid);
      const docRef = await addDoc(collection(db, SAVED_IDEAS_COLLECTION), ideaToSave);
      console.log('Idea saved successfully with ID:', docRef.id);
      return docRef.id;
    } catch (error) {
      console.error('Error saving idea:', error);
      throw new Error(`Failed to save idea: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get all saved ideas for a specific user
   */
  static async getUserIdeas(userId: string): Promise<SavedIdea[]> {
    try {
      // Ensure user is authenticated
      const user = await ensureAuthenticated();
      
      // Verify the userId matches the authenticated user
      if (user.uid !== userId) {
        throw new Error('Cannot access ideas for different user.');
      }

      console.log('Fetching saved ideas for user:', user.uid);
      
      // First try the optimized query with orderBy (requires index)
      try {
        const q = query(
          collection(db, SAVED_IDEAS_COLLECTION),
          where('userId', '==', user.uid),
          orderBy('createdAt', 'desc')
        );

        const querySnapshot = await getDocs(q);
        const ideas: SavedIdea[] = [];

        console.log('Optimized query succeeded. Found', querySnapshot.size, 'saved ideas');

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          ideas.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
          } as SavedIdea);
        });

        return ideas;
      } catch (indexError) {
        console.warn('Optimized query failed (index building), using fallback:', indexError);
        
        // Fallback: Get all user ideas without orderBy, then sort client-side
        const q = query(
          collection(db, SAVED_IDEAS_COLLECTION),
          where('userId', '==', user.uid)
        );

        const querySnapshot = await getDocs(q);
        const ideas: SavedIdea[] = [];

        console.log('Fallback query succeeded. Found', querySnapshot.size, 'saved ideas (will sort client-side)');

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          ideas.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
          } as SavedIdea);
        });

        // Sort client-side by createdAt desc
        ideas.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
        
        return ideas;
      }
    } catch (error) {
      console.error('Error fetching user ideas:', error);
      
      // Provide more specific error messages
      if (error instanceof Error && error.message.includes('index is currently building')) {
        throw new Error('DATABASE_BUILDING');
      } else if (error instanceof Error && error.message.includes('requires an index')) {
        throw new Error('DATABASE_BUILDING');
      }
      
      throw new Error(`Failed to fetch ideas: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get a specific saved idea by ID
   */
  static async getIdea(ideaId: string): Promise<SavedIdea | null> {
    try {
      // Ensure user is authenticated
      await ensureAuthenticated();
      
      const ideaRef = doc(db, SAVED_IDEAS_COLLECTION, ideaId);
      const ideaSnap = await getDoc(ideaRef);

      if (!ideaSnap.exists()) {
        return null;
      }

      const data = ideaSnap.data();
      return {
        id: ideaSnap.id,
        ...data,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
      } as SavedIdea;
    } catch (error) {
      console.error('Error fetching idea:', error);
      throw new Error('Failed to fetch idea');
    }
  }

  /**
   * Delete a saved idea
   */
  static async deleteIdea(ideaId: string): Promise<void> {
    try {
      // Ensure user is authenticated
      await ensureAuthenticated();
      
      const ideaRef = doc(db, SAVED_IDEAS_COLLECTION, ideaId);
      await deleteDoc(ideaRef);
      console.log('Idea deleted successfully:', ideaId);
    } catch (error) {
      console.error('Error deleting idea:', error);
      throw new Error(`Failed to delete idea: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if a book title is already saved as an idea
   */
  static async isIdeaAlreadySaved(userId: string, bookTitle: BookTitle): Promise<boolean> {
    try {
      // Ensure user is authenticated
      const user = await ensureAuthenticated();
      
      // Verify the userId matches the authenticated user
      if (user.uid !== userId) {
        throw new Error('Cannot check ideas for different user.');
      }

      console.log('Checking if idea already saved for user:', user.uid, 'BookTitle ID:', bookTitle.id);
      
      // First try the compound query (requires index)
      try {
        const q = query(
          collection(db, SAVED_IDEAS_COLLECTION),
          where('userId', '==', user.uid),
          where('bookTitle.id', '==', bookTitle.id)
        );

        const querySnapshot = await getDocs(q);
        const exists = !querySnapshot.empty;
        
        console.log('Compound query succeeded. Idea already saved:', exists, 'Query returned', querySnapshot.size, 'documents');
        return exists;
      } catch (indexError) {
        console.warn('Compound query failed (likely missing index), falling back to client-side filtering:', indexError);
        
        // Fallback: Get all user ideas and check client-side
        const q = query(
          collection(db, SAVED_IDEAS_COLLECTION),
          where('userId', '==', user.uid)
        );

        const querySnapshot = await getDocs(q);
        const exists = querySnapshot.docs.some(doc => {
          const data = doc.data();
          return data.bookTitle?.id === bookTitle.id;
        });
        
        console.log('Client-side filtering result. Idea already saved:', exists, 'Checked', querySnapshot.size, 'documents');
        return exists;
      }
    } catch (error) {
      console.error('Error checking if idea is already saved:', error);
      
      // For any other errors, return false to allow saving (better UX than blocking)
      console.warn('Allowing save operation due to error in duplicate check');
      return false;
    }
  }
}